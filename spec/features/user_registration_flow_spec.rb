require "rails_helper"

RSpec.feature "User Registration Flow", type: :feature do
  let(:country) { create(:country, code: "CA", name: "Canada") }
  let(:region) { create(:region, name: "North America") }
  let(:state) { create(:state, name: "Ontario", region: region) }
  let(:store) { create(:store, name: "Zeiss Optical Toronto", status: :active) }

  before do
    # Create address for the store
    store.create_address(
      street: "123 Queen St",
      city: "Toronto",
      postal_code: "M5H 2M9",
      country: country,
      state: state
    )
  end

  scenario "User completes full registration flow" do
    # Step 1: Visit landing page
    visit root_path
    expect(page).to have_content("ZeissPoints")
    expect(page).to have_content("Your rewards journey starts here")

    # Step 2: Click Create Account
    click_link "Create Account"
    expect(current_path).to eq(search_stores_path)
    expect(page).to have_content("Find Your Store")
    expect(page).to have_content("Step 1 of 3")

    # Step 3: Search for store
    fill_in "q_name_cont", with: "Zeiss"
    click_button "Search Stores"

    expect(page).to have_content("Select Your Store")
    expect(page).to have_content("Zeiss Optical Toronto")
    expect(page).to have_content("Toronto, Ontario")

    # Step 4: Select store
    click_button "Select"
    expect(current_path).to eq(new_user_registration_path)
    expect(page).to have_content("Create Your Account")
    expect(page).to have_content("Step 2 of 3")
    expect(page).to have_content("Selected Store")
    expect(page).to have_content("Zeiss Optical Toronto")

    # Step 5: Fill registration form
    fill_in "Email Address", with: "<EMAIL>"
    fill_in "Street Address", with: "456 Main St"
    fill_in "City", with: "Toronto"
    fill_in "Postal Code", with: "M4E 1A1"
    select "Ontario", from: "user_address_attributes_state_id"
    select "Canada", from: "user_address_attributes_country_id"
    fill_in "Password", with: "password123"
    fill_in "Confirm Password", with: "password123"

    # Step 6: Submit registration
    click_button "Create Account"

    # Should be redirected to confirmation page
    expect(page).to have_content("A message with a confirmation link has been sent to your email address")

    # Step 7: Manually confirm the user (simulate clicking confirmation link)
    user = User.find_by(email: "<EMAIL>")
    user.confirm
    user.update!(status: :active)  # Also activate the user

    # Step 8: Sign in the confirmed user
    visit new_user_session_path
    fill_in "Email Address", with: "<EMAIL>"
    fill_in "Password", with: "password123"
    click_button "Sign In"

    # Should be redirected to dashboard
    expect(current_path).to eq(root_path)
    expect(page).to have_content("Welcome back, John")
    expect(page).to have_content("Quick Actions")
    expect(page).to have_content("Add Sale")
  end

  scenario "User searches for non-existent store and adds new one" do
    visit search_stores_path

    # Search for non-existent store
    fill_in "q_name_cont", with: "Non-existent Store"
    click_button "Search Stores"

    expect(page).to have_content("No stores found")
    expect(page).to have_content("Can't find your store?")

    # Fill out new store form
    fill_in "Store Name", with: "New Optical Store"
    fill_in "Phone Number", with: "************"
    fill_in "Street Address", with: "789 Bay St"
    fill_in "City", with: "Toronto"
    fill_in "Postal Code", with: "M5G 1M1"
    select "Ontario", from: "address[state_id]"
    select "Canada", from: "address[country_id]"

    expect {
      click_button "Request Store Addition"
    }.to change(Store, :count).by(1)

    new_store = Store.last
    expect(new_store.name).to eq("New Optical Store")
    expect(new_store.status).to eq("requested")
  end

  scenario "User cannot select future sale dates", js: true do
    # Create user and sign in
    user = create(:user)
    sign_in_with_capybara(user)

    # Create necessary data
    brand = create(:brand)
    store_chain = create(:store_chain)
    store.update(store_chain: store_chain, brand: brand)
    user.update(store: store)

    # Create address for user (required for sale validation)
    country = Country.find_or_create_by(code: "CA") { |c| c.name = "Canada" }
    admin_user = create(:user, email: "<EMAIL>", role: :admin, store: store)
    region = create(:region, admin_user: admin_user)
    state = create(:state, region: region)
    user.create_address(country: country, state: state, street: "123 Main", city: "Test", postal_code: "12345")

    category = create(:category, brand: brand)
    product = create(:product, category: category)

    # Create product country data for points calculation
    create(:product_country_datum, product: product, country: country, points_earned: 100)

    visit new_sale_path

    expect(page).to have_content("Add Sale")
    expect(page).to have_field("sale_sold_at", with: Date.current.to_s)

    # Try to set future date using JavaScript (simulating user interaction)
    future_date = 1.week.from_now.strftime("%Y-%m-%d")
    # Remove the max attribute first to allow future dates
    page.execute_script("document.getElementById('sale_sold_at').removeAttribute('max')")
    page.execute_script("document.getElementById('sale_sold_at').value = '#{future_date}'")

    select product.name, from: "sale_product_id"
    fill_in "Serial Number", with: "TEST123456"
    click_button "Record Sale"

    # Verify that the validation worked correctly:
    # 1. No sale was created
    # 2. We stayed on the form page (indicating validation failed)
    expect(Sale.count).to eq(0)
    expect(current_path).to eq(new_sale_path) # Should stay on the form page
  end
end
