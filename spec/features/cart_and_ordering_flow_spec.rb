require "warden/test/helpers"

RSpec.configure do |config|
  config.before(:suite) { Warden.test_mode! }
  config.after(:each) { Warden.test_reset! }
end
require "rails_helper"

RSpec.describe "Cart and Ordering Flow", type: :request do
  let!(:brand) { create(:brand) }
  let!(:store_chain) { create(:store_chain) }
  let!(:store) { create(:store, :active, store_chain: store_chain, brand: brand) }
  let!(:user) { create(:user, store: store, status: :active, confirmed_at: Time.current) }
  let!(:category) { create(:category, brand: brand) }
  let!(:product1) { create(:product, category: category, name: "Test Camera") }
  let!(:product2) { create(:product, category: category, name: "Test Lens") }
  let!(:country) { create(:country, code: "CA", name: "Canada") }
  let!(:address) { create(:address, addressable: user, country: country) }

  before do
    # Create product country data for points
    create(:product_country_datum, product: product1, country: country, points_cost: 300, msrp: 299.99)
    create(:product_country_datum, product: product2, country: country, points_cost: 150, msrp: 149.99)

    login_as(user, scope: :user)
  end

  describe "User adds products to cart and places order" do
    it "allows user to add products to cart and complete checkout" do
      # Add first product to cart via line items
      expect {
        post line_items_path, params: {product_id: product1.id}
      }.to change(LineItem, :count).by(1)

      expect(response).to redirect_to(cart_path(user.cart))
      follow_redirect!
      expect(response.body).to include("Product added to cart")
      expect(response.body).to include("Test Camera")

      # Add second product to cart
      expect {
        post line_items_path, params: {product_id: product2.id}
      }.to change(LineItem, :count).by(1)

      expect(response).to redirect_to(cart_path(user.cart))
      follow_redirect!
      expect(response.body).to include("Product added to cart")

      # Verify both items are in cart
      cart = user.reload.cart
      expect(cart.line_items.count).to eq(2)
      expect(cart.line_items.map(&:product)).to include(product1, product2)

      # Update quantity of first item
      line_item = cart.line_items.find_by(product: product1)
      patch line_item_path(line_item), params: {line_item: {quantity: 2}}
      expect(response).to redirect_to(cart_path(cart))
      follow_redirect!
      expect(response.body).to include("Cart updated")

      # Verify quantity was updated
      expect(line_item.reload.quantity).to eq(2)

      # For points-based system, cart total should be 0 (no monetary price)
      expect(cart.reload.total_price).to eq(0.0)

      # Proceed to checkout
      get new_order_path
      expect(response).to have_http_status(:success)

      # Place order
      expect {
        post orders_path, params: {
          order: {
            shipping_type: "user",
            shipping_address: "123 Main Street, Test City, TC 12345"
          }
        }
      }.to change(Order, :count).by(1)

      expect(response).to redirect_to(orders_path)
      follow_redirect!
      expect(response.body).to include("Order placed and pending approval")

      # Verify order details
      order = Order.last
      expect(order.line_items.count).to eq(2)
      expect(order.total).to eq(0.0) # Points-based system has no monetary total
      expect(order.shipping_type).to eq("user")
      expect(order.shipping_address).to eq("123 Main Street, Test City, TC 12345")
      expect(order.status).to eq("pending")
      expect(order.points).to be > 0 # Should have points calculated

      # Verify cart is destroyed
      expect(Cart.find_by(id: user.cart&.id)).to be_nil
    end
  end

  describe "User removes items from cart" do
    it "allows user to remove individual items from cart" do
      # Add items to cart first
      post line_items_path, params: {product_id: product1.id}
      post line_items_path, params: {product_id: product2.id}

      cart = user.reload.cart
      expect(cart.line_items.count).to eq(2)

      # Remove first item
      line_item = cart.line_items.find_by(product: product1)
      expect {
        delete line_item_path(line_item)
      }.to change(LineItem, :count).by(-1)

      expect(response).to redirect_to(cart_path(cart))
      follow_redirect!
      expect(response.body).to include("Item removed from cart")

      # Verify only second item remains
      expect(cart.reload.line_items.count).to eq(1)
      expect(cart.line_items.first.product).to eq(product2)
    end
  end

  describe "User empties entire cart" do
    it "allows user to empty their cart" do
      # Add items to cart
      post line_items_path, params: {product_id: product1.id}

      cart = user.reload.cart
      expect(cart.line_items.count).to eq(1)

      # Empty cart
      delete cart_path(cart)
      expect(response).to redirect_to(root_path)
      follow_redirect!
      expect(response.body).to include("Cart was successfully emptied")

      # Verify cart is destroyed
      expect(Cart.find_by(id: cart.id)).to be_nil
    end
  end

  describe "User tries to checkout with empty cart" do
    it "redirects to cart when trying to checkout with empty cart" do
      get new_order_path

      expect(response).to redirect_to(cart_path(user.cart))
      follow_redirect!
      expect(response.body).to include("Your cart is empty")
    end
  end

  describe "User ships to store instead of address" do
    it "allows user to ship to store" do
      # Add item to cart
      post line_items_path, params: {product_id: product1.id}

      # Place order with store shipping
      expect {
        post orders_path, params: {
          order: {
            shipping_type: "store",
            shipping_address: ""
          }
        }
      }.to change(Order, :count).by(1)

      # Verify order
      order = Order.last
      expect(order.shipping_type).to eq("store")
      expect(order.shipping_address).to be_blank
      expect(response).to redirect_to(orders_path)
    end
  end

  describe "Order validation errors are handled" do
    it "handles validation errors when creating order" do
      # Add item to cart
      post line_items_path, params: {product_id: product1.id}

      # Try to create order without required shipping_type
      expect {
        post orders_path, params: {
          order: {
            shipping_type: "",
            shipping_address: "123 Main St"
          }
        }
      }.not_to change(Order, :count)

      expect(response).to have_http_status(:unprocessable_entity)
    end
  end

  describe "Guest user cart functionality" do
    it "allows guest users to add items to cart" do
      logout(:user)

      # Simulate adding to guest cart (application will create cart automatically)
      expect {
        post line_items_path, params: {product_id: product1.id}
      }.to change(LineItem, :count).by(1)

      # Verify line item was added to a guest cart
      line_item = LineItem.last
      expect(line_item.cart.user).to be_nil
      expect(response).to redirect_to(cart_path(line_item.cart))
    end
  end

  describe "Cart persists across sessions for logged in users" do
    it "maintains cart items when user logs back in" do
      # Add item to cart
      post line_items_path, params: {product_id: product1.id}

      cart = user.reload.cart
      expect(cart.line_items.count).to eq(1)

      # Sign out and back in
      logout(:user)
      login_as(user, scope: :user)

      # Cart should still have items
      expect(user.reload.cart).to eq(cart)
      expect(user.cart.line_items.count).to eq(1)
      expect(user.cart.line_items.first.product).to eq(product1)
    end
  end

  describe "Multiple quantity updates work correctly" do
    it "allows updating line item quantities multiple times" do
      # Add item to cart
      post line_items_path, params: {product_id: product1.id}

      cart = user.reload.cart
      line_item = cart.line_items.first
      expect(line_item.quantity).to eq(1)

      # Update to quantity 3
      patch line_item_path(line_item), params: {line_item: {quantity: 3}}
      expect(response).to redirect_to(cart_path(cart))
      follow_redirect!
      expect(response.body).to include("Cart updated")

      expect(line_item.reload.quantity).to eq(3)
      expected_points = product1.points_required * 3
      expect(cart.total_points).to eq(expected_points)

      # Update to quantity 1
      patch line_item_path(line_item), params: {line_item: {quantity: 1}}
      expect(response).to redirect_to(cart_path(cart))
      follow_redirect!
      expect(response.body).to include("Cart updated")

      expect(line_item.reload.quantity).to eq(1)
      expect(cart.reload.total_points).to eq(product1.points_required)
    end
  end

  describe "Adding same product multiple times increases quantity" do
    it "increments quantity when same product is added multiple times" do
      # Add product first time
      post line_items_path, params: {product_id: product1.id}

      cart = user.reload.cart
      expect(cart.line_items.count).to eq(1)
      line_item = cart.line_items.first
      expect(line_item.quantity).to eq(1)

      # Add same product again
      post line_items_path, params: {product_id: product1.id}

      # Should still have only 1 line item but with quantity 2
      expect(cart.reload.line_items.count).to eq(1)
      expect(line_item.reload.quantity).to eq(2)

      # Verify total points reflects quantity
      expected_points = product1.points_required * 2
      expect(cart.total_points).to eq(expected_points)
    end
  end

  describe "Cart shows correct item totals" do
    it "displays correct points totals for individual items and cart" do
      # Add items with different quantities
      post line_items_path, params: {product_id: product1.id}
      post line_items_path, params: {product_id: product2.id}

      cart = user.reload.cart
      expect(cart.line_items.count).to eq(2)

      # Update first item to quantity 2
      line_item1 = cart.line_items.find_by(product: product1)
      patch line_item_path(line_item1), params: {line_item: {quantity: 2}}

      # Check individual item totals (in points)
      expect(line_item1.reload.quantity).to eq(2)
      line_item2 = cart.line_items.find_by(product: product2)

      camera_total_points = product1.points_required * 2  # 300 * 2 = 600
      lens_total_points = product2.points_required * 1    # 150 * 1 = 150
      grand_total_points = camera_total_points + lens_total_points  # 750

      expect(line_item1.total_points).to eq(camera_total_points)
      expect(line_item2.total_points).to eq(lens_total_points)
      expect(cart.reload.total_points).to eq(grand_total_points)
    end
  end

  describe "Order preserves line item details correctly" do
    it "maintains line item quantities and details when creating order" do
      # Add items to cart
      post line_items_path, params: {product_id: product1.id}
      post line_items_path, params: {product_id: product2.id}

      # Update quantities
      cart = user.reload.cart
      line_item1 = cart.line_items.find_by(product: product1)
      line_item2 = cart.line_items.find_by(product: product2)

      patch line_item_path(line_item1), params: {line_item: {quantity: 3}}
      patch line_item_path(line_item2), params: {line_item: {quantity: 2}}

      # Place order
      expect {
        post orders_path, params: {
          order: {
            shipping_type: "user",
            shipping_address: "456 Test Ave"
          }
        }
      }.to change(Order, :count).by(1)

      # Verify order line items
      order = Order.last
      camera_line_item = order.line_items.find_by(product: product1)
      lens_line_item = order.line_items.find_by(product: product2)

      expect(camera_line_item.quantity).to eq(3)
      expect(camera_line_item.price).to eq(0.0)  # Points-based system
      expect(lens_line_item.quantity).to eq(2)
      expect(lens_line_item.price).to eq(0.0)    # Points-based system

      # Verify order totals
      expect(order.total).to eq(0.0)  # No monetary total in points system
      expected_points = (product1.points_required * 3) + (product2.points_required * 2)
      expect(order.points).to eq(expected_points)
    end
  end
end
