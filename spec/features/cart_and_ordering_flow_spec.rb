require "warden/test/helpers"

RSpec.configure do |config|
  config.before(:suite) { Warden.test_mode! }
  config.after(:each) { Warden.test_reset! }
end
require "rails_helper"

RSpec.describe "Cart and Ordering Flow", type: :request do
  let!(:brand) { create(:brand) }
  let!(:store_chain) { create(:store_chain) }
  let!(:store) { create(:store, :active, store_chain: store_chain, brand: brand) }
  let!(:user) { create(:user, store: store, status: :active, confirmed_at: Time.current) }
  let!(:category) { create(:category, brand: brand) }
  let!(:product1) { create(:product, category: category, name: "Test Camera") }
  let!(:product2) { create(:product, category: category, name: "Test Lens") }
  let!(:country) { create(:country, code: "CA", name: "Canada") }
  let!(:address) { create(:address, addressable: user, country: country) }

  before do
    # Create product country data for points
    create(:product_country_datum, product: product1, country: country, points_cost: 300, msrp: 299.99)
    create(:product_country_datum, product: product2, country: country, points_cost: 150, msrp: 149.99)

    login_as(user, scope: :user)
  end

  describe "User adds products to cart and places order" do
    it "allows user to add products to cart and complete checkout" do
      # Add first product to cart via line items
      expect {
        post line_items_path, params: {product_id: product1.id}
      }.to change(LineItem, :count).by(1)

      expect(response).to redirect_to(cart_path(user.cart))
      follow_redirect!
      expect(response.body).to include("Product added to cart")
      expect(response.body).to include("Test Camera")

      # Add second product to cart
      expect {
        post line_items_path, params: {product_id: product2.id}
      }.to change(LineItem, :count).by(1)

      expect(response).to redirect_to(cart_path(user.cart))
      follow_redirect!
      expect(response.body).to include("Product added to cart")

      # Verify both items are in cart
      cart = user.reload.cart
      expect(cart.line_items.count).to eq(2)
      expect(cart.line_items.map(&:product)).to include(product1, product2)

      # Update quantity of first item
      line_item = cart.line_items.find_by(product: product1)
      patch line_item_path(line_item), params: {line_item: {quantity: 2}}
      expect(response).to redirect_to(cart_path(cart))
      follow_redirect!
      expect(response.body).to include("Cart updated")

      # Verify quantity was updated
      expect(line_item.reload.quantity).to eq(2)

      # For points-based system, cart total should be 0 (no monetary price)
      expect(cart.reload.total_price).to eq(0.0)

      # Proceed to checkout
      get new_order_path
      expect(response).to have_http_status(:success)

      # Place order
      expect {
        post orders_path, params: {
          order: {
            shipping_type: "user",
            shipping_address: "123 Main Street, Test City, TC 12345"
          }
        }
      }.to change(Order, :count).by(1)

      expect(response).to redirect_to(orders_path)
      follow_redirect!
      expect(response.body).to include("Order placed and pending approval")

      # Verify order details
      order = Order.last
      expect(order.line_items.count).to eq(2)
      expect(order.total).to eq(0.0) # Points-based system has no monetary total
      expect(order.shipping_type).to eq("user")
      expect(order.shipping_address).to eq("123 Main Street, Test City, TC 12345")
      expect(order.status).to eq("pending")
      expect(order.points).to be > 0 # Should have points calculated

      # Verify cart is destroyed
      expect(Cart.find_by(id: user.cart&.id)).to be_nil
    end
  end

  describe "User removes items from cart" do
    it "allows user to remove individual items from cart" do
      # Add items to cart first
      post line_items_path, params: {product_id: product1.id}
      post line_items_path, params: {product_id: product2.id}

      cart = user.reload.cart
      expect(cart.line_items.count).to eq(2)

      # Remove first item
      line_item = cart.line_items.find_by(product: product1)
      expect {
        delete line_item_path(line_item)
      }.to change(LineItem, :count).by(-1)

      expect(response).to redirect_to(cart_path(cart))
      follow_redirect!
      expect(response.body).to include("Item removed from cart")

      # Verify only second item remains
      expect(cart.reload.line_items.count).to eq(1)
      expect(cart.line_items.first.product).to eq(product2)
    end
  end

  describe "User empties entire cart" do
    it "allows user to empty their cart" do
      # Add items to cart
      post line_items_path, params: {product_id: product1.id}

      cart = user.reload.cart
      expect(cart.line_items.count).to eq(1)

      # Empty cart
      delete cart_path(cart)
      expect(response).to redirect_to(root_path)
      follow_redirect!
      expect(response.body).to include("Cart was successfully emptied")

      # Verify cart is destroyed
      expect(Cart.find_by(id: cart.id)).to be_nil
    end
  end

  describe "User tries to checkout with empty cart" do
    it "redirects to cart when trying to checkout with empty cart" do
      get new_order_path

      expect(response).to redirect_to(cart_path(user.cart))
      follow_redirect!
      expect(response.body).to include("Your cart is empty")
    end
  end

  scenario "User ships to store instead of address" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    click_button "Checkout"

    # Select store shipping
    select "My Store", from: "order[shipping_type]"

    # Place order
    click_button "Place Order"

    # Verify order
    order = Order.last
    expect(order.shipping_type).to eq("store")
    expect(order.shipping_address).to be_blank
  end

  scenario "Order validation errors are handled" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    click_button "Checkout"

    # Submit without required shipping type
    fill_in "order[shipping_address]", with: ""

    # This would need to be adjusted based on actual validation behavior
    # The form might prevent submission or show validation errors
  end

  scenario "Guest user cart functionality" do
    sign_out user

    # Create a guest cart session
    guest_cart = create(:cart, user: nil)

    # Simulate adding to guest cart
    expect {
      post line_items_path, params: {product_id: product1.id}
    }.to change(LineItem, :count).by(1)

    # Note: Guest checkout would require additional implementation
    # This test demonstrates the cart functionality works for guests
  end

  scenario "Cart persists across sessions for logged in users" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    # Sign out and back in
    sign_out user
    sign_in user

    # Visit cart - should still have items
    visit cart_path(user.cart)
    expect(page).to have_content("Test Camera")
  end

  scenario "Multiple quantity updates work correctly" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)

    # Update to quantity 3
    within("tr", text: "Test Camera") do
      fill_in "line_item[quantity]", with: "3"
      click_button "Update"
    end

    expect(page).to have_content("Cart updated")
    total = 299.99 * 3
    expect(page).to have_content("$#{sprintf("%.2f", total)}")

    # Update to quantity 1
    within("tr", text: "Test Camera") do
      fill_in "line_item[quantity]", with: "1"
      click_button "Update"
    end

    expect(page).to have_content("$299.99")
  end

  scenario "Adding same product multiple times increases quantity" do
    # Add product first time
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    expect(page).to have_field("line_item[quantity]", with: "1")

    # Add same product again
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    expect(page).to have_field("line_item[quantity]", with: "2")

    # Verify total reflects quantity
    total = 299.99 * 2
    expect(page).to have_content("$#{sprintf("%.2f", total)}")
  end

  scenario "Cart shows correct item totals" do
    # Add items with different quantities
    post line_items_path, params: {product_id: product1.id}
    post line_items_path, params: {product_id: product2.id}

    visit cart_path(user.cart)

    # Update first item to quantity 2
    within("tr", text: "Test Camera") do
      fill_in "line_item[quantity]", with: "2"
      click_button "Update"
    end

    # Check individual item totals
    camera_total = 299.99 * 2
    lens_total = 149.99 * 1
    grand_total = camera_total + lens_total

    expect(page).to have_content("$#{sprintf("%.2f", camera_total)}")
    expect(page).to have_content("$#{sprintf("%.2f", lens_total)}")
    expect(page).to have_content("Total: $#{sprintf("%.2f", grand_total)}")
  end

  scenario "Order preserves line item details correctly" do
    # Add items to cart
    post line_items_path, params: {product_id: product1.id}
    post line_items_path, params: {product_id: product2.id}

    # Update quantities
    cart = user.cart
    cart.line_items.find_by(product: product1).update!(quantity: 3)
    cart.line_items.find_by(product: product2).update!(quantity: 2)

    visit cart_path(cart)
    click_button "Checkout"

    select "My Address", from: "order[shipping_type]"
    fill_in "order[shipping_address]", with: "456 Test Ave"

    click_button "Place Order"

    # Verify order line items
    order = Order.last
    camera_line_item = order.line_items.find_by(product: product1)
    lens_line_item = order.line_items.find_by(product: product2)

    expect(camera_line_item.quantity).to eq(3)
    expect(camera_line_item.price).to eq(299.99)
    expect(lens_line_item.quantity).to eq(2)
    expect(lens_line_item.price).to eq(149.99)

    expected_total = (299.99 * 3) + (149.99 * 2)
    expect(order.total).to eq(expected_total)
  end
end
