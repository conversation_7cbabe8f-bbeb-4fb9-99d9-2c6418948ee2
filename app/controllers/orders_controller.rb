class OrdersController < ApplicationController
  before_action :authenticate_user!

  def index
    brand = current_user.store&.brand
    @products = brand ? Product.joins(:category).where(categories: {brand: brand}) : Product.none
  end

  def new
    @cart = current_cart
    if @cart.line_items.empty?
      redirect_to cart_path(@cart), alert: "Your cart is empty."
    else
      @order = Order.new
    end
  end

  def create
    @cart = current_cart
    if @cart.line_items.empty?
      redirect_to cart_path(@cart), alert: "Your cart is empty."
      return
    end
    @order = current_user.orders.build(order_params)
    @order.total = 0.0  # Points-based system has no monetary total
    @order.status = :pending
    @order.points = @cart.total_points
    if @order.save
      @cart.line_items.find_each do |item|
        item.update!(order: @order, cart: nil)
      end
      @cart.destroy
      session[:cart_id] = nil
      redirect_to orders_path, notice: "Order placed and pending approval."
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def order_params
    params.require(:order).permit(:shipping_type, :shipping_address)
  end
end
