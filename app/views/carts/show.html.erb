<h1 class="text-2xl font-bold mb-4">Your Cart</h1>
<% if @cart.line_items.any? %>
  <table class="min-w-full divide-y divide-gray-200 mb-4">
    <thead>
      <tr>
        <th class="px-4 py-2">Product</th>
        <th class="px-4 py-2">Quantity</th>
        <th class="px-4 py-2">Points Each</th>
        <th class="px-4 py-2">Total Points</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <% @cart.line_items.includes(:product).each do |item| %>
        <tr>
          <td class="px-4 py-2"><%= item.product.name %></td>
          <td class="px-4 py-2">
            <%= form_with(model: item, url: line_item_path(item), method: :patch, local: true) do |f| %>
              <%= f.number_field :quantity, min: 1, value: item.quantity, class: "w-16 border rounded px-2 py-1" %>
              <%= f.submit 'Update', class: "ml-2 text-blue-600" %>
            <% end %>
          </td>
          <td class="px-4 py-2"><%= item.product.points_required %> points</td>
          <td class="px-4 py-2"><%= item.total_points %> points</td>
          <td class="px-4 py-2">
            <%= button_to 'Remove', line_item_path(item), method: :delete, data: { turbo_confirm: 'Remove this item?' }, class: "text-red-600" %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
  <div class="flex justify-between items-center">
    <div class="text-lg font-semibold">Total: <%= @cart.total_points %> points</div>
    <%= button_to 'Checkout', new_order_path, method: :get, class: "bg-green-600 text-white px-4 py-2 rounded" %>
  </div>
<% else %>
  <p>Your cart is empty.</p>
<% end %>
